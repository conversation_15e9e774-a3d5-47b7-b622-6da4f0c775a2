import sounddevice as sd
import numpy as np
import librosa
import matplotlib.pyplot as plt
import librosa.display
from scipy.io.wavfile import write
from tensorflow.keras.models import load_model
import json
import os

# === SETTINGS ===
DURATION = 6  # seconds
SAMPLE_RATE = 22050  # Hz
MODEL_PATH = 'best_cnn_model.h5'  # Trained model
LABEL_MAP_PATH = 'label_map.json'  # Class index-to-name map
AUDIO_PATH = "test_input.wav"

# === RECORD AUDIO ===
print("🎙️ Recording for 6 seconds...")
recording = sd.rec(int(DURATION * SAMPLE_RATE), samplerate=SAMPLE_RATE, channels=1, dtype='float32')
sd.wait()
write(AUDIO_PATH, SAMPLE_RATE, recording)
print(f"✅ Saved recording to {AUDIO_PATH}")

# === FEATURE EXTRACTION FUNCTION ===
def extract_features(audio_path):
    y, sr = librosa.load(audio_path, sr=SAMPLE_RATE)

    spec_contrast = np.mean(librosa.feature.spectral_contrast(y=y, sr=sr), axis=1)
    tonnetz = np.mean(librosa.feature.tonnetz(y=librosa.effects.harmonic(y), sr=sr), axis=1)
    chroma = np.mean(librosa.feature.chroma_stft(y=y, sr=sr), axis=1)
    mel = np.mean(librosa.power_to_db(librosa.feature.melspectrogram(y=y, sr=sr)), axis=1)
    mfcc = np.mean(librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13), axis=1)

    return np.concatenate([spec_contrast, tonnetz, chroma, mel, mfcc])

# === LOAD MODEL ===
if not os.path.exists(MODEL_PATH):
    raise FileNotFoundError(f"❌ Model file not found at: {MODEL_PATH}")
model = load_model(MODEL_PATH)
print("📦 Model loaded successfully.")

# === LOAD LABEL MAP ===
if not os.path.exists(LABEL_MAP_PATH):
    raise FileNotFoundError(f"❌ Label map not found: {LABEL_MAP_PATH}")
with open(LABEL_MAP_PATH, "r") as f:
    label_map = json.load(f)
index_to_label = {v: k for k, v in label_map.items()}

# === EXTRACT FEATURES ===
print("🧪 Extracting features...")
features = extract_features(AUDIO_PATH)
features = features.reshape(1, -1, 1)  # Match (batch, features, 1) shape

# === PREDICT ===
print("🔍 Predicting...")
prediction = model.predict(features)
predicted_index = int(np.argmax(prediction))
confidence = float(np.max(prediction))
predicted_label = index_to_label[predicted_index]

# === DISPLAY RESULT ===
print("\n📣 PREDICTION RESULT:")
if predicted_label == "gun_shot":
    print(f"🚨 Gunshot Detected! (Confidence: {confidence:.2f})")
else:
    print(f"✅ Detected: {predicted_label} (Confidence: {confidence:.2f})")

# === VISUALIZE MEL-SPECTROGRAM ===
def plot_mel_spectrogram(audio_path):
    y, sr = librosa.load(audio_path, sr=SAMPLE_RATE)
    S = librosa.feature.melspectrogram(y=y, sr=sr)
    S_dB = librosa.power_to_db(S, ref=np.max)

    plt.figure(figsize=(10, 4))
    librosa.display.specshow(S_dB, sr=sr, x_axis='time', y_axis='mel')
    plt.colorbar(format='%+2.0f dB')
    plt.title("Mel-Spectrogram")
    plt.tight_layout()
    plt.show()

# Call the plot function
plot_mel_spectrogram(AUDIO_PATH)