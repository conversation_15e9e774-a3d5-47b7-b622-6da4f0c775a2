from flask import Flask, jsonify
from flask_cors import CORS
from download_logs import download_logs
from gps_location import get_current_location

import sounddevice as sd
import numpy as np
import librosa
from scipy.io.wavfile import write
from keras.models import load_model
import json
from datetime import datetime, timezone
import matplotlib.pyplot as plt
import librosa.display

app = Flask(__name__)
app.register_blueprint(download_logs)

# Enable CORS for frontend
CORS(app, resources={r"/*": {"origins": "http://localhost:5173"}})

# === CONFIG ===
DURATION = 6  # seconds
SAMPLE_RATE = 22050
AUDIO_PATH = "test_input.wav"
MODEL_PATH = "best_cnn_model.h5"
LABEL_MAP_PATH = "label_map.json"

# === Load model + label map once ===
model = load_model(MODEL_PATH)

with open(LABEL_MAP_PATH, "r") as f:
    label_map = json.load(f)

# Reverse label map: index → label
index_to_label = {v: k for k, v in label_map.items()}


# === Feature Extraction ===
def extract_features(audio_path):
    y, sr = librosa.load(audio_path, sr=SAMPLE_RATE)
    spec_contrast = np.mean(librosa.feature.spectral_contrast(y=y, sr=sr), axis=1)
    tonnetz = np.mean(librosa.feature.tonnetz(y=librosa.effects.harmonic(y), sr=sr), axis=1)
    chroma = np.mean(librosa.feature.chroma_stft(y=y, sr=sr), axis=1)
    mel = np.mean(librosa.power_to_db(librosa.feature.melspectrogram(y=y, sr=sr)), axis=1)
    mfcc = np.mean(librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13), axis=1)
    return np.concatenate([spec_contrast, tonnetz, chroma, mel, mfcc])


# === Plot Mel-Spectrogram ===
def show_mel_spectrogram(audio_path):
    y, sr = librosa.load(audio_path, sr=SAMPLE_RATE)
    S = librosa.feature.melspectrogram(y=y, sr=sr)
    S_dB = librosa.power_to_db(S, ref=np.max)

    plt.figure(figsize=(10, 4))
    librosa.display.specshow(S_dB, sr=sr, x_axis='time', y_axis='mel')
    plt.colorbar(format='%+2.0f dB')
    plt.title("Mel-Spectrogram")
    plt.tight_layout()
    plt.show()


# === Prediction Route ===
@app.route("/predict", methods=["POST"])
def predict():
    try:
        # Record live audio
        recording = sd.rec(int(DURATION * SAMPLE_RATE), samplerate=SAMPLE_RATE, channels=1, dtype='float32')
        sd.wait()
        write(AUDIO_PATH, SAMPLE_RATE, recording)

        # Show Mel Spectrogram for debugging
        show_mel_spectrogram(AUDIO_PATH)

        # Extract features
        features = extract_features(AUDIO_PATH).reshape(1, -1, 1)

        # Predict
        prediction = model.predict(features)
        predicted_index = int(np.argmax(prediction))
        confidence = float(np.max(prediction))
        predicted_label = index_to_label[predicted_index]

        # 🔊 Print prediction to terminal
        print(f"[🔊 Prediction] Label: {predicted_label} | Confidence: {confidence:.4f}")

        # Determine threat status
        status = "danger" if predicted_label == "gun_shot" else "safe"

        # Construct response
        return jsonify({
            "label": predicted_label,
            "confidence": round(confidence * 100, 2),
            "status": status,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/location', methods=['GET'])
def get_location():
    location = get_current_location()
    return jsonify(location)

if __name__ == "__main__":
    app.run(debug=True)