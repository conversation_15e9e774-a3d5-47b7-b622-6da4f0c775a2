absl-py==2.3.0
astunparse==1.6.3
audioread==3.0.1
blinker==1.9.0
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
contourpy==1.3.2
cycler==0.12.1
decorator==5.2.1
Flask==3.1.1
flask-cors==6.0.1
flatbuffers==25.2.10
fonttools==4.58.4
gast==0.6.0
google-api-core==2.25.1
google-auth==2.40.3
google-cloud-core==2.4.3
google-cloud-firestore==2.21.0
google-pasta==0.2.0
googleapis-common-protos==1.70.0
grpcio==1.73.0
grpcio-status==1.73.0
h5py==3.14.0
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.6
joblib==1.5.1
keras==3.10.0
kiwisolver==1.4.8
lazy_loader==0.4
libclang==18.1.1
librosa==0.11.0
llvmlite==0.44.0
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.3
mdurl==0.1.2
ml_dtypes==0.5.1
msgpack==1.1.1
namex==0.1.0
numba==0.61.2
numpy==2.1.3
opt_einsum==3.4.0
optree==0.16.0
packaging==25.0
pillow==11.2.1
platformdirs==4.3.8
pooch==1.8.2
proto-plus==1.26.1
protobuf>=6.30.0,<7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
Pygments==2.19.1
pyparsing==3.2.3
python-dateutil==2.9.0.post0
requests==2.32.4
rich==14.0.0
rsa==4.9.1
scikit-learn==1.7.0
scipy==1.15.3
six==1.17.0
sounddevice==0.5.2
soundfile==0.13.1
soxr==0.5.0.post1
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorflow==2.19.0
tensorflow-io-gcs-filesystem==0.31.0
termcolor==3.1.0
threadpoolctl==3.6.0
typing_extensions==4.14.0
urllib3==2.4.0
Werkzeug==3.1.3
wrapt==1.17.2
