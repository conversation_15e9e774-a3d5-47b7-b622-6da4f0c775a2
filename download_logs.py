from flask import Blueprint, request, jsonify, send_file
from google.cloud import firestore
from datetime import datetime, timedelta
import csv
import os

download_logs = Blueprint("download_logs", __name__)
db = firestore.Client()

@download_logs.route("/export-logs", methods=["POST"])
def export_logs():
    data = request.get_json()
    range_type = data.get("range")  # "daily", "weekly", "monthly"
    
    now = datetime.utcnow()
    
    if range_type == "daily":
        start_time = now - timedelta(days=1)
    elif range_type == "weekly":
        start_time = now - timedelta(weeks=1)
    elif range_type == "monthly":
        start_time = now - timedelta(days=30)
    else:
        return jsonify({"error": "Invalid range"}), 400

    alerts_ref = db.collection("alerts")
    query = alerts_ref.where("timestamp", ">=", start_time)
    docs = query.stream()

    filename = f"alerts_{range_type}_{now.strftime('%Y%m%d%H%M%S')}.csv"
    filepath = os.path.join("/tmp", filename)

    with open(filepath, mode="w", newline="") as csvfile:
        fieldnames = ["timestamp", "classification", "label", "lat", "lng", "addedBy", "clearedBy", "clearedAt"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for doc in docs:
            data = doc.to_dict()
            writer.writerow({
                "timestamp": data.get("timestamp").isoformat() if "timestamp" in data else "",
                "classification": data.get("classification", ""),
                "label": data.get("label", ""),
                "lat": data.get("location", {}).get("lat", ""),
                "lng": data.get("location", {}).get("lng", ""),
                "addedBy": data.get("addedBy", ""),
                "clearedBy": data.get("clearedBy", ""),
                "clearedAt": data.get("clearedAt").isoformat() if "clearedAt" in data else "",
            })

    return send_file(filepath, mimetype="text/csv", as_attachment=True, download_name=filename)